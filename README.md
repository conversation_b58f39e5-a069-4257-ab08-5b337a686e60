# 💔 The Ex-Terminator: AugmentCode Data Cleaner v1.1
## *"Because sometimes you need a clean break from your digital past"*

Just like your ex who kept showing up at your favorite coffee shop, AugmentCode data has a way of lingering around your computer long after you've moved on. This tool is here to help you achieve what your ex never could: **a complete and total disappearance!**

**Now with enhanced email cleaning** - because unlike your ex, we actually help you move on! 💅

## ⚠️ Important Disclaimer (Unlike Your Ex's Promises)

**This tool is provided for educational and personal use only.** Always backup your data before use - we're more reliable than your ex's "I'll change" promises, but we still recommend caution! The authors are not responsible for any data loss or issues that may arise from using this tool. Use at your own risk (which is still lower than dating your ex again).

## 🚀 Features (Better Than Your Ex's Feature List)

### Core Functionality (Actually Works, Unlike Your Ex)
- **Automatic Discovery**: Finds AugmentCode data faster than your ex found your Netflix password
- **Email Address Cleaning**: 🆕 Removes email addresses more thoroughly than your ex removed themselves from your life
- **Telemetry ID Modification**: Resets device IDs better than your ex reset their relationship status
- **Database Cleaning**: Removes 'augment' records like your ex should have removed their toxic behavior
- **Workspace Management**: Cleans workspace storage - finally, something that actually cleans up after itself!
- **Cross-Platform Support**: Works on Windows, macOS, and Linux (unlike your ex who only worked on your nerves)

### Safety Features (More Reliable Than Your Ex's Promises)
- **Comprehensive Backup System**: Creates backups before changes (unlike your ex who just created problems)
- **Detailed Logging**: Tracks everything with timestamps - better documentation than your ex's excuses
- **Confirmation Dialogs**: Actually asks before doing something destructive (revolutionary concept!)
- **Restore Functionality**: Easy restoration from backups - if only relationships had this feature
- **Non-Destructive Discovery**: Safe scanning without modifications (your ex could learn from this)

### User Interface (Easier to Navigate Than Your Ex's Mood Swings)
- **Modern GUI**: Intuitive interface that doesn't require a psychology degree to understand
- **Real-Time Progress**: Shows actual progress, not just empty promises
- **Discovery Reports**: Detailed reports that are actually accurate (shocking, we know)
- **Backup Management**: Easy viewing and restoration - like a time machine, but useful

## 📋 Requirements (Less Demanding Than Your Ex)

- Python 3.7 or higher (more stable than your ex's mental state)
- Operating System: Windows, macOS, or Linux (we're not picky, unlike your ex)
- Administrator/root privileges (the only time we'll ask you to give us power over your life)

### Python Dependencies (Won't Ghost You Like Your Ex)
Most dependencies are part of Python's standard library (they actually come when called):
- `tkinter` (GUI framework - prettier than your ex's personality)
- `sqlite3` (database operations - more organized than your ex's life)
- `pathlib` (file path handling - knows where it's going, unlike your ex)
- `threading` (background operations - works behind the scenes without drama)
- `logging` (activity logging - keeps better records than your ex's memory)

Optional dependencies (see `requirements.txt`):
- `pyinstaller` (for creating standalone executables - independence at its finest!)

## 🛠️ Installation (Easier Than Getting Over Your Ex)

### Option 1: Run from Source (The "I Can Do This Myself" Approach)
1. Clone or download this repository (it won't leave you hanging)
2. Install Python 3.7+ if not already installed (more reliable than your ex's promises)
3. Install optional dependencies (they actually show up when needed):
   ```bash
   pip install -r requirements.txt
   ```
4. Run the application (it actually runs, imagine that!):
   ```bash
   python main.py
   ```

### Option 2: Create Standalone Executable (The "Independence Day" Method)
1. Install PyInstaller (it won't ask for your Netflix password):
   ```bash
   pip install pyinstaller
   ```
2. Create executable (more self-sufficient than your ex ever was):
   ```bash
   pyinstaller --onefile --windowed main.py
   ```
3. Find the executable in the `dist` folder (it'll be exactly where it says it is - revolutionary!)

## 🎯 Email Reuse Solution (The "Fresh Start" Package)

### The Problem (Like Your Ex, But Digital)
Many users want to reuse the same email address after their AugmentCode plan expires, but the system may detect and block this. It's like your ex recognizing you at the grocery store even after you changed your hair - awkward and unwanted. The enhanced cleaner solves this by:

- **Removing email traces**: Finds and removes email addresses more thoroughly than your ex removed their stuff from your apartment
- **Cleaning account data**: Removes user records like your ex should have removed themselves from your life
- **Resetting device identity**: Changes device IDs to appear as a completely new machine (witness protection program for your computer)
- **Clearing authentication**: Removes cached login data faster than your ex cleared their browser history

### The Result (Better Than Therapy)
✅ **Same email can be reused** - All local traces are removed (unlike your ex's lingering presence)
✅ **Device appears new** - Fresh device fingerprint generated (new phone, who dis?)
✅ **Clean authentication state** - No cached login data remains (clean slate, clean conscience)
✅ **Safe operation** - Complete backup system for easy restoration (unlike your relationship, this is reversible)

**See [EMAIL_CLEANING_GUIDE.md](EMAIL_CLEANING_GUIDE.md) for detailed instructions (clearer than your ex's mixed signals).**

## 📖 Usage Guide (Simpler Than Your Ex's Mood Swings)

### Getting Started (The "Moving On" Process)
1. **Launch the Application**: Run `python main.py` or use the standalone executable (it actually starts when you want it to)
2. **Auto-Detect or Browse**: Use "Auto-Detect" to find AugmentCode automatically, or "Browse" to select manually (we give you choices, unlike your ex)
3. **Run Discovery**: Click "Discover AugmentCode Data" to scan for data locations (like a digital detective, but useful)
4. **Review Results**: Check the "Discovery" tab for detailed findings (transparency - what a concept!)
5. **Configure Options**: Select desired cleanup operations in the main tab (customizable, unlike your ex's personality)
6. **Run Cleanup**: Click "Run Cleanup" to perform the selected operations (it actually does what it says it will do)

### Main Tab Features (All Actually Functional)

#### AugmentCode Location (GPS for Your Data)
- **Auto-Detect**: Automatically searches common installation directories (finds things without you having to explain where they are)
- **Browse**: Manually select the AugmentCode installation folder (for when you want control)
- **Path Display**: Shows the currently selected path (honest about where it's going)

#### Cleanup Options (The Breakup Toolkit)
- **Modify Telemetry IDs**: Changes device and machine IDs to new random values (new identity, new you)
- **Clean Database**: Removes database records containing 'augment' keyword (selective memory deletion)
- **Clean Account Data**: 🆕 Removes email addresses and user account information (digital amnesia at its finest)
- **Clean Workspace Storage**: Removes cache files, temporary files, and session data (spring cleaning for your soul)
- **Create Backup**: Creates a backup before any modifications (safety net, unlike your relationship)

#### Email-Specific Options (The "Block and Delete" Special)
- **Target Email**: Specify a specific email address to remove (precision strikes available)
- **Remove All Accounts**: Remove all discovered account data for complete reset (scorched earth policy)

#### Action Buttons (They Actually Do Things)
- **Discover AugmentCode Data**: Scans for all AugmentCode-related data (finds everything, unlike your ex who "couldn't find" their commitment)
- **Run Cleanup**: Performs selected cleanup operations (follows through on promises)
- **Generate Report**: Creates a detailed discovery report (documentation that actually exists)

### Discovery Tab (The "Investigation Files")
- **Detailed Results**: Shows all discovered data locations (more thorough than your ex's apologies)
- **File Listings**: Lists configuration files, databases, and workspace locations (organized, unlike your ex's priorities)
- **Save Report**: Export discovery results to a text file (evidence for later, just in case)

### Backup Tab (The "Time Machine")
- **Backup List**: View all available backups with timestamps and sizes (better record-keeping than your ex's promises)
- **Restore**: Restore data from a selected backup (undo button for life - if only!)
- **Delete**: Remove old backups to free space (Marie Kondo for your digital life)
- **Refresh**: Update the backup list (stays current, unlike your ex's excuses)

## 🔧 Technical Details (The Nerdy Stuff Your Ex Never Understood)

### Data Discovery Process (Better Detective Work Than Finding Your Ex's Red Flags)
The tool searches for AugmentCode data in these locations (it actually looks where it says it will):

**Windows (The Complicated One):**
- `%APPDATA%\AugmentCode` (where secrets hide)
- `%LOCALAPPDATA%\AugmentCode` (local drama storage)
- `%PROGRAMDATA%\AugmentCode` (shared problems)
- `%PROGRAMFILES%\AugmentCode` (the official residence)

**macOS (The Pretentious One):**
- `~/Library/Application Support/AugmentCode` (fancy hiding spot)
- `/Library/Application Support/AugmentCode` (system-wide drama)
- `~/.config/AugmentCode` (configuration confusion)

**Linux (The Honest One):**
- `~/.config/AugmentCode` (straightforward, unlike your ex)
- `~/.local/share/AugmentCode` (personal space that's actually respected)
- `/opt/AugmentCode` (optional, like your ex should have been)

### Telemetry ID Modification (Digital Witness Protection Program)
- Searches configuration files (JSON, INI, XML) for ID patterns (more thorough than your ex's background checks)
- Generates new UUIDs for device and machine identifiers (new identity, new life)
- Updates Windows registry entries (Windows only) - deep cleaning where it counts
- Backs up original values before modification (safety first, unlike your relationship choices)

### Database Cleaning (Digital Therapy Session)
- Identifies SQLite database files (finds the emotional baggage)
- Searches for tables containing user/session data (digs deep into the issues)
- Removes records with 'augment' keyword in text fields (selective amnesia)
- Executes VACUUM to reclaim disk space (spring cleaning for your soul)

### Workspace Management (Feng Shui for Your Files)
- Finds workspace directories and project folders (knows where everything lives)
- Identifies cleanable items (cache, temporary files, session data) - separates trash from treasure
- Provides selective cleaning options (you choose what goes, unlike your ex who took everything)
- Preserves important project files (protects what matters)

## 🛡️ Safety Measures (More Protection Than Your Ex Ever Gave You)

### Backup System (The Safety Net Your Relationship Needed)
- **Timestamped Backups**: Each backup has a unique timestamp (better record-keeping than your ex's alibis)
- **Comprehensive Coverage**: Backs up files, directories, and registry data (covers all the bases)
- **Manifest Files**: JSON manifests track what was backed up (paper trail that actually exists)
- **Easy Restoration**: One-click restore functionality (undo button for life)

### Error Handling (Dealing With Problems Like an Adult)
- **Permission Checks**: Handles access denied errors gracefully (takes "no" for an answer)
- **File Locking**: Detects when files are in use (respects boundaries)
- **Rollback Capability**: Can restore from backup if operations fail (has a plan B, C, and D)
- **Detailed Logging**: All operations are logged for troubleshooting (accountability - what a concept!)

## 🐛 Troubleshooting (When Things Go Wrong, Unlike Your Ex Who Just Went)

### Common Issues (Less Drama Than Your Ex's Problems)

**"Permission Denied" Errors (The Digital Restraining Order):**
- Run as Administrator (Windows) or with sudo (Linux/macOS) - sometimes you need the big guns
- Close AugmentCode before running the cleaner - no cheating while we're working
- Check file permissions in the AugmentCode directory - make sure we're actually invited to the party

**"No AugmentCode Data Found" (Playing Hide and Seek):**
- Verify AugmentCode is installed - it might be ghosting you like your ex
- Try manual path selection with Browse button - sometimes you have to point things out
- Check if AugmentCode uses a non-standard installation directory - it might be as unpredictable as your ex's location

**"Database Locked" Errors (The Commitment Issues):**
- Ensure AugmentCode is completely closed - no half-measures here
- Check for background AugmentCode processes - make sure it's not lurking around
- Restart the computer if necessary - the classic "turn it off and on again" (works better than relationship advice)

**Backup/Restore Issues (When Your Safety Net Has Holes):**
- Verify sufficient disk space for backups - we need room to work our magic
- Check backup directory permissions - make sure we can actually save you
- Ensure backup files haven't been moved or deleted - unlike your ex, we need these to stick around

### Getting Help (Actual Support, Imagine That!)
1. Check the activity log in the main tab for detailed error messages (clearer than your ex's communication)
2. Review the discovery report for data location issues (better detective work than finding your ex's red flags)
3. Verify all AugmentCode processes are closed before running cleanup (no multitasking during breakups)
4. Try running with administrator privileges (sometimes you need to be the boss)

## 📝 License (The Fine Print, But Funnier)

This project is provided as-is for educational purposes. Users are responsible for ensuring compliance with applicable terms of service and local laws. (We're not your lawyers, just like your ex wasn't your therapist.)

## 🤝 Contributing (Join the Support Group)

This tool is designed for personal use. If you encounter issues or have suggestions for improvements, please ensure any modifications maintain the safety-first approach and comprehensive backup system. (We believe in healthy relationships with our code, unlike some people we know.)

## 📞 Support (Better Than Your Ex's Emotional Support)

This tool is provided without warranty or support. Users should:
- Always test in a safe environment first (unlike your relationship choices)
- Maintain their own backups (learn from your mistakes)
- Use at their own risk (still safer than texting your ex at 2 AM)
- Understand the implications of modifying application data (read the room, unlike your ex)

---

## 💔 Final Words of Wisdom

Just like getting over your ex, using this tool is about moving forward, not looking back. Sometimes you need to delete the digital traces to make room for something better.

**Remember: Always backup your data before using any data modification tool!**

*Because unlike your ex, your data might actually be worth keeping.* 💅

---

**P.S.** If this tool works better than your last relationship, consider it a win! 🎉

**P.P.S.** No exes were harmed in the making of this README (emotionally or otherwise). 😇
